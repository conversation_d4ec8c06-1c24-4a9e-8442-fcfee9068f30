# Separated GraphQL Endpoints

## Overview

The xbit-agent service now provides two separate GraphQL endpoints:

1. **User GraphQL API** - For user-facing operations with JWT authentication
2. **Admin GraphQL API** - For admin-only operations with API key authentication

## Endpoints

### User GraphQL API
- **Endpoint**: `POST /api/dex-agent/graphql`
- **Playground**: `GET /api/dex-agent/graphql/playground`
- **Authentication**: JWT Token (Bearer)
- **Purpose**: User operations like referrals, rewards, transactions, etc.

### Admin GraphQL API
- **Endpoint**: `POST /api/dex-agent/admin/graphql`
- **Playground**: `GET /api/dex-agent/admin/graphql/playground`
- **Authentication**: API Key
- **Purpose**: Admin operations like task management, user statistics, system administration

## Authentication

### User Authentication (JWT)

```bash
curl -X POST http://localhost:8080/api/dex-agent/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{"query": "{ referralSnapshot { totalReferrals } }"}'
```

### Admin Authentication (API Key)

```bash
curl -X POST http://localhost:8080/api/dex-agent/admin/graphql \
  -H "Content-Type: application/json" \
  -H "x-api-key: <api_key>" \
  -d '{"query": "{ adminGetAllTasks { id name } }"}'
```

Alternative API key headers:
- `X-API-Key: <api_key>`
- `Authorization: Bearer <api_key>`

## Available Operations

### User Operations (JWT Required)
- `referralSnapshot` - Get user referral data
- `agentLevels` - Get agent level information
- `userLevelInfo` - Get current user level info
- `transactionData` - Get user transaction data
- `dataOverview` - Get user data overview
- `rewardData` - Get user reward data
- `claimAgentReferral` - Claim referral rewards
- `createUserWithReferral` - Create user with referral code
- And more user-facing operations...

### Admin Operations (API Key Required)
- `adminGetAllTasks` - Get all activity tasks
- `adminGetTaskCompletionStats` - Get task completion statistics
- `adminGetUserActivityStats` - Get user activity statistics
- `adminGetTierDistribution` - Get tier distribution
- `adminGetTopUsers` - Get top users by points
- `createTask` - Create new activity task
- `updateTask` - Update existing task
- `deleteTask` - Delete task
- `adminResetDailyTasks` - Reset all daily tasks
- `adminRecalculateAllUserTiers` - Recalculate user tiers
- And more admin operations...

## Configuration

### Environment Variables

Add the admin GraphQL prefix to your environment configuration:

```bash
# User GraphQL endpoint (existing)
GRAPHQL_PREFIX="/api/dex-agent/graphql"

# Admin GraphQL endpoint (new)
ADMIN_GRAPHQL_PREFIX="/api/dex-agent/admin/graphql"

# Admin API key for authentication
INTERNAL_API_KEY="your-secure-api-key-here"
```

### Config File (config.yaml)

```yaml
system:
  graphql-prefix: "/api/dex-agent/graphql"
  admin-graphql-prefix: "/api/dex-agent/admin/graphql"

admin:
  internal-api-key: {{ index . "INTERNAL_API_KEY" | default "default-key" }}
```

## Security Considerations

1. **API Key Security**: Store admin API keys securely and rotate them regularly
2. **Network Security**: Consider restricting admin endpoint access to internal networks
3. **Monitoring**: Monitor admin endpoint usage for security auditing
4. **Rate Limiting**: Apply appropriate rate limiting to both endpoints

## Migration Notes

- Existing user applications continue to work without changes
- Admin operations previously using `@adminAuth` directive are now on separate endpoint
- No breaking changes for existing user-facing GraphQL queries/mutations
- Admin clients need to update to use new admin endpoint and API key authentication

## Testing

### Test User Endpoint
```bash
# Test user GraphQL with JWT
curl -X POST http://localhost:8080/api/dex-agent/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{"query": "{ agentLevels { id name } }"}'
```

### Test Admin Endpoint
```bash
# Test admin GraphQL with API key
curl -X POST http://localhost:8080/api/dex-agent/admin/graphql \
  -H "Content-Type: application/json" \
  -H "x-api-key: <api_key>" \
  -d '{"query": "{ adminGetAllTasks { id name displayName } }"}'
```

## Implementation Notes

- **Same Schema, Different Authentication**: Both endpoints use the same GraphQL schema but with different authentication middleware
- **User Endpoint**: Uses JWT authentication for user-facing operations
- **Admin Endpoint**: Uses API key authentication, but can access all operations (including admin-only ones)
- **Backward Compatibility**: Existing user applications continue to work without changes
- **Admin Operations**: Previously protected by `@adminAuth` directive, now protected by endpoint-level API key authentication
- **No Breaking Changes**: All existing user-facing GraphQL queries/mutations work as before

## Troubleshooting

### Common Issues

1. **401 Unauthorized on Admin Endpoint**
   - Check API key is correct
   - Verify header name (`x-api-key`, `X-API-Key`, or `Authorization: Bearer`)
   - Ensure API key matches `INTERNAL_API_KEY` environment variable

2. **404 Not Found**
   - Verify endpoint URLs are correct
   - Check server configuration for admin GraphQL prefix

3. **Schema Errors**
   - Admin operations are only available on admin endpoint
   - User operations are only available on user endpoint
   - Check you're using the correct endpoint for your operation type
