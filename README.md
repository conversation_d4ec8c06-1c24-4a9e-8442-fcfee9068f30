# XBIT Agent

XBIT Agent is a Go-based GraphQL API service for managing user authentication, wallet management, agent referral systems, and activity cashback for the XBIT DEX platform.

## Features

- **User Management**: Create and manage user accounts with email-based authentication
- **Wallet Integration**: Support for embedded and managed wallet types across multiple chains
- **Agent Referral System**: Multi-level referral tracking with invitation codes and commission distribution
- **Activity Cashback**: Cashback system for trading activities and user engagement
- **GraphQL API**: Modern GraphQL interface for all operations
- **Database Migrations**: Atlas-based database schema management
- **Docker Support**: Containerized deployment with Docker Compose

## Architecture

This project follows the clean architecture pattern inspired by `xbit-goback`:

```
├── cmd/                    # Application entry points
│   ├── graphql/           # GraphQL server
│   └── atlasloader/       # Database schema loader
├── config/                # Configuration structures
├── internal/
│   ├── app/              # Application initialization
│   ├── controller/       # GraphQL controllers and resolvers
│   ├── global/           # Global variables
│   ├── initializer/      # Service initializers
│   ├── model/            # Database models
│   ├── repo/             # Repository layer
│   ├── service/          # Business logic layer
│   └── utils/            # Utility functions
├── migrations/           # Database migrations
└── pkg/                  # Shared packages
```

## Database Schema

### Users Table
- `id` (UUID, Primary Key)
- `email` (TEXT, Unique)
- `invitation_code` (TEXT, 5-15 characters, user input, initially null)

### User Wallets Table
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key)
- `chain` (TEXT, Not Null)
- `name` (TEXT)
- `wallet_address` (TEXT, Not Null)
- `wallet_id` (UUID)
- `wallet_account_id` (UUID)
- `wallet_type` (TEXT, 'embedded' or 'managed')

### Referrals Table
- `id` (SERIAL, Primary Key)
- `user_id` (UUID, Foreign Key, Unique)
- `referrer_id` (UUID, Foreign Key)
- `depth` (INT, Default: 1)
- `created_at` (TIMESTAMP)

### Referral Snapshots Table
- `user_id` (UUID, Primary Key)
- `direct_count` (INT, Default: 0)
- `total_downline_count` (INT, Default: 0)
- `total_volume_usd` (NUMERIC(38,2), Default: 0)
- `total_rewards_distributed` (NUMERIC(38,6), Default: 0)

## GraphQL API

### Key Mutations

#### Login/Create User
```graphql
mutation Login($input: LoginInput!) {
  login(input: $input) {
    user {
      id
      email
      invitationCode
    }
    token
    message
  }
}
```

#### Create User Wallet
```graphql
mutation CreateUserWallet($input: CreateUserWalletInput!) {
  createUserWallet(input: $input) {
    wallet {
      id
      walletAddress
      chain
      walletType
    }
    success
    message
  }
}
```

#### Update User Invitation Code
```graphql
mutation UpdateUserInvitationCode($input: UpdateUserInvitationCodeInput!) {
  updateUserInvitationCode(input: $input) {
    id
    email
    invitationCode
  }
}
```

### Key Queries

#### Get User Information
```graphql
query GetUser($id: ID!) {
  user(id: $id) {
    id
    email
    wallets {
      id
      chain
      walletAddress
    }
    referralSnapshot {
      directCount
      totalDownlineCount
    }
  }
}
```

#### Get Referral Downlines
```graphql
query GetDownlines($userId: ID!) {
  downlines(userId: $userId) {
    id
    userId
    depth
    user {
      email
    }
  }
}
```

## Database Migration Strategy

This project uses **Atlas migrations** for database schema management:

### All Environments
- Uses **Atlas migrations** for explicit schema control
- Migration files are applied consistently across all environments
- Provides version control for database schema changes
- Allows for rollbacks and precise schema management

### Migration Files
- Migration files are tracked in git
- Generated using Atlas CLI from GORM models
- Applied in all environments (development, staging, production)
- Provides explicit control over database schema changes

## Getting Started

### Prerequisites

- Go 1.21+
- PostgreSQL 14+
- Atlas CLI (for migrations)

### Installation

1. Clone the repository:
```bash
git clone https://gitlab.ggwp.life/xbit/xbit-dex/xbit-agent.git
cd xbit-agent
```

2. Install dependencies:
```bash
go mod tidy
```

3. Install Atlas CLI:
```bash
# macOS
brew install ariga/tap/atlas

# Linux
curl -sSf https://atlasgo.sh | sh
```

4. Database Setup:

**Note**: This project uses **Atlas migrations** for database schema management in all environments.

```bash
# Generate migration file when models change
make db-diff

# Apply migrations to database
make db-apply
# or using scripts
./scripts/run.sh local migrate

# For production - migrations are applied via entrypoint.sh or CI/CD
```

5. Build and run(local):
```bash
make build-local
make run-local
# or using scripts
./scripts/run.sh local run
```

### Docker Deployment

1. Start with Docker Compose:
```bash
docker-compose up -d
```

This will start:
- PostgreSQL database on port 5432
- XBIT Agent API on port 8080

2. Access GraphQL Playground:
```
http://localhost:8080/playground
```

## User Flow

### Login Process

1. **User Login**: When a user logs into XBIT, they provide:
   - Email address
   - Optional: Wallet address and signature
   - Optional: Referrer invitation code

2. **User Creation**: If user doesn't exist:
   - Create new user record with email
   - `invitation_code` is initially null
   - If referrer code provided, establish referral relationship

3. **Agent Referral System**:
   - Users can be referred by providing a referrer's invitation code
   - Agent referral relationships are tracked in the `referrals` table
   - Referral snapshots maintain aggregate statistics for commission calculation

4. **Invitation Code Generation**:
   - Invitation codes are generated on-demand when needed
   - Users start without invitation codes
   - Codes are unique 8-character hex strings

## API Endpoints

### User API (JWT Authentication)
- **User GraphQL API**: `POST /api/dex-agent/graphql`
- **User GraphQL Playground**: `GET /api/dex-agent/graphql/playground`
- **User Health Check**: `GET /api/dex-agent/graphql/ping` or `GET /api/dex-agent/graphql/healthz`

### Admin API (API Key Authentication)
- **Admin GraphQL API**: `POST /api/dex-agent/admin/graphql`
- **Admin GraphQL Playground**: `GET /api/dex-agent/admin/graphql/playground`
- **Admin Health Check**: `GET /api/dex-agent/admin/graphql/ping` or `GET /api/dex-agent/admin/graphql/healthz`

### Authentication

#### User Authentication (JWT)
- Add `Authorization: Bearer <jwt_token>` header to requests
- JWT tokens contain user ID and email claims
- Used for all user-facing operations

#### Admin Authentication (API Key)
- Add `x-api-key: <api_key>` header to requests
- Alternative headers: `X-API-Key` or `Authorization: Bearer <api_key>`
- Used for all admin-only operations
- Configure API key via `INTERNAL_API_KEY` environment variable

## Configuration

### Environment Variables

The application uses environment variables for configuration, similar to xbit-goback. Available environment files:

- `.env.example` - Template with all available options
- `env/local.env` - Local development settings
- `env/docker.env` - Docker environment settings
- `env/production.env` - Production template (update before use)

Key environment variables:

```bash
# Database
POSTGRES_AGENCY_HOST=127.0.0.1
POSTGRES_AGENCY_PORT=5432
POSTGRES_AGENCY_USER=postgres
POSTGRES_AGENCY_PASS=postgres
POSTGRES_AGENCY_SSL_MODE=disable
POSTGRES_DB=agent

# Server
SERVER_PORT=8080
APP_ENV=local

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_TIME=7d

# Redis (Optional)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
```

### Configuration Files

The `config.yaml` file uses Go templates to read environment variables:

```yaml
system:
  addr: {{ index . "SERVER_PORT" | default "8080" }}
  db-type: pgsql

pgsql:
  path: {{ index . "POSTGRES_AGENCY_HOST" | default "127.0.0.1" }}
  port: {{ index . "POSTGRES_AGENCY_PORT" | default "5432" }}
  config: sslmode={{ index . "POSTGRES_AGENCY_SSL_MODE" | default "disable" }}
  db-name: {{ index . "POSTGRES_DB" | default "agent" }}
  username: {{ index . "POSTGRES_AGENCY_USER" | default "postgres" }}
  password: {{ index . "POSTGRES_AGENCY_PASS" | default "postgres" }}
```

### Running with Different Environments

```bash
# Local development
./scripts/run.sh local run

# Docker environment
./scripts/run.sh docker run

# Production environment
./scripts/run.sh production run

# Run migrations for specific environment
./scripts/run.sh local migrate
./scripts/run.sh docker migrate
```

## Testing

```bash
make test
```

## License

This project is proprietary to XBIT DEX.