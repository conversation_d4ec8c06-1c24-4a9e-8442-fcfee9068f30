package app

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_error"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/middlewares"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
)

type GraphqlServer struct {
	Router     *gin.Engine
	HttpServer *http.Server
	ctx        context.Context
	Cancel     context.CancelFunc
}

func (server *GraphqlServer) Initialize() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		// initializer.Redis()
		// initializer.RedisList()
	}

	// Database tables are managed by Atlas migrations
	// No auto-migration needed

	// Initialize NATS clients - only using nats-meme for now
	// initializer.InitNats() // Temporarily disabled - only using nats-meme for now
	initializer.InitNatsMeme()
	// initializer.InitNatsDex() // Temporarily disabled - not yet implemented for affiliate events

	// Create context for NATS subscribers
	server.ctx, server.Cancel = context.WithCancel(context.Background())

	// Start affiliate subscriber
	if err := initializer.StartAffiliateSubscriber(server.ctx); err != nil {
		global.GVA_LOG.Error("Failed to start affiliate subscriber", zap.Error(err))
	}
}

func (server *GraphqlServer) Run() {
	if server.Router == nil {
		server.Router = server.setupRouter()
	}

	address := fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr)
	server.HttpServer = &http.Server{
		Addr:           address,
		Handler:        server.Router,
		ReadTimeout:    20 * time.Second,
		WriteTimeout:   20 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	initializer.InitializeAgentLevels()
	global.GVA_LOG.Info("InitializeAgentLevels started")

	fmt.Printf(`
	Welcome to XBIT Agent
	Current Version: v1.0.0
	User GraphQL Playground: http://127.0.0.1%s%s/playground
	User GraphQL Endpoint: http://127.0.0.1%s%s
	Admin GraphQL Playground: http://127.0.0.1%s%s/playground
	Admin GraphQL Endpoint: http://127.0.0.1%s%s
`, address, global.GVA_CONFIG.System.GraphqlPrefix, address, global.GVA_CONFIG.System.GraphqlPrefix,
		address, global.GVA_CONFIG.System.AdminGraphqlPrefix, address, global.GVA_CONFIG.System.AdminGraphqlPrefix)

	log.Fatal(server.HttpServer.ListenAndServe())
}

func (server *GraphqlServer) setupRouter() *gin.Engine {
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	router.Use(middlewares.GinGqlContext()) // this will attach gin.Context into graphql context

	// Health check routes (following xbit-goback convention)
	pingHandler := func(c *gin.Context) {
		c.JSON(200, gin.H{
			"pong": time.Now().UnixMilli(),
		})
	}

	// User GraphQL API group with prefix
	userAPI := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)
	userAPI.GET("/ping", pingHandler)
	userAPI.GET("/healthz", pingHandler)

	// Admin GraphQL API group with prefix
	adminAPI := router.Group(global.GVA_CONFIG.System.AdminGraphqlPrefix)
	adminAPI.GET("/ping", pingHandler)
	adminAPI.GET("/healthz", pingHandler)

	// Initialize both GraphQL servers
	initUserGraphqlServer(router)
	initAdminGraphqlServer(router)

	return router
}

func initUserGraphqlServer(router *gin.Engine) {
	api := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)

	// User GraphQL playground and server
	playGroundHandler := playground.Handler("XBIT Agent: User GraphQL playground", fmt.Sprintf("%s", global.GVA_CONFIG.System.GraphqlPrefix))
	graphqlServer := handler.NewDefaultServer(
		graphql.NewExecutableSchema(graphql.Config{
			Resolvers: graphql.NewRootResolver(),
		}),
	)
	graphqlServer.SetErrorPresenter(gql_error.CustomErrorPresenter)

	api.GET("/playground", func(c *gin.Context) {
		playGroundHandler.ServeHTTP(c.Writer, c.Request)
	})

	api.POST("", middlewares.GqlJwtAuth(), func(c *gin.Context) {
		graphqlServer.ServeHTTP(c.Writer, c.Request)
	})
}

func initAdminGraphqlServer(router *gin.Engine) {
	api := router.Group(global.GVA_CONFIG.System.AdminGraphqlPrefix)

	// Admin GraphQL playground and server
	playGroundHandler := playground.Handler("XBIT Agent: Admin GraphQL playground", fmt.Sprintf("%s", global.GVA_CONFIG.System.AdminGraphqlPrefix))

	// Use same GraphQL schema but with admin resolver and API key authentication
	graphqlServer := handler.NewDefaultServer(
		graphql.NewExecutableSchema(graphql.Config{
			Resolvers: graphql.NewRootResolver(), // Use same schema but different authentication
		}),
	)
	graphqlServer.SetErrorPresenter(gql_error.CustomErrorPresenter)

	api.GET("/playground", func(c *gin.Context) {
		playGroundHandler.ServeHTTP(c.Writer, c.Request)
	})

	api.POST("", middlewares.ApiKeyAuth(), func(c *gin.Context) {
		graphqlServer.ServeHTTP(c.Writer, c.Request)
	})
}
