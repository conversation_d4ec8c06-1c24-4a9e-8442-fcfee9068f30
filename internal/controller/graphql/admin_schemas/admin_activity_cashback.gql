# Admin Activity Cashback Types and Inputs
# These types are used by admin-only operations

# Admin Input Types
input AdminStatsInput {
  startDate: Time!
  endDate: Time!
}

input CreateTaskInput {
  name: String!
  displayName: String!
  description: String
  categoryId: ID!
  taskType: TaskType!
  frequency: TaskFrequency!
  pointsReward: Int!
  cashbackReward: Float
  maxCompletions: Int
  isActive: Boolean!
  sortOrder: Int
  requirements: TaskRequirementsInput
}

input UpdateTaskInput {
  id: ID!
  name: String
  displayName: String
  description: String
  categoryId: ID
  taskType: TaskType
  frequency: TaskFrequency
  pointsReward: Int
  cashbackReward: Float
  maxCompletions: Int
  isActive: Boolean
  sortOrder: Int
  requirements: TaskRequirementsInput
}

input CreateTaskCategoryInput {
  name: String!
  displayName: String!
  description: String
}

input UpdateTaskCategoryInput {
  id: ID!
  name: String
  displayName: String
  description: String
}

input CreateTierBenefitInput {
  tierLevel: Int!
  benefitType: String!
  benefitValue: Float!
  description: String
}

input UpdateTierBenefitInput {
  id: ID!
  tierLevel: Int
  benefitType: String
  benefitValue: Float
  description: String
}

input TaskRequirementsInput {
  minAmount: Float
  targetCount: Int
  specificActions: [String!]
}

# Admin Response Types
type AdminTaskCompletionStatsResponse {
  success: Boolean!
  message: String!
  data: AdminTaskCompletionStats
}

type AdminTaskCompletionStats {
  taskCompletions: [TaskCompletionStat!]!
  startDate: Time!
  endDate: Time!
  totalTasks: Int!
}

type TaskCompletionStat {
  taskName: String!
  completionCount: Int!
}

type AdminUserActivityStatsResponse {
  success: Boolean!
  message: String!
  data: AdminUserActivityStats
}

type AdminUserActivityStats {
  dailyCompletions: [DailyCompletionStat!]!
  startDate: Time!
  endDate: Time!
}

type DailyCompletionStat {
  date: String!
  completionCount: Int!
}

type AdminTierDistributionResponse {
  success: Boolean!
  message: String!
  data: [TierDistributionStat!]!
}

type TierDistributionStat {
  tierLevel: Int!
  userCount: Int!
}

type TierBenefitResponse {
  success: Boolean!
  message: String!
  data: TierBenefit
}

# Shared Types (referenced from main schema)
type ActivityTask {
  id: ID!
  name: String!
  displayName: String!
  description: String
  category: TaskCategory!
  taskType: TaskType!
  frequency: TaskFrequency!
  pointsReward: Int!
  cashbackReward: Float
  maxCompletions: Int
  isActive: Boolean!
  sortOrder: Int!
  requirements: TaskRequirements
  createdAt: Time!
  updatedAt: Time!
}

type TaskCategory {
  id: ID!
  name: String!
  displayName: String!
  description: String
  isActive: Boolean!
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
}

type UserTierInfo {
  userId: ID!
  email: String
  currentTier: Int!
  totalPoints: Int!
  totalCashback: Float!
  completedTasks: Int!
  lastActivityAt: Time
}

type TierBenefit {
  id: ID!
  tierLevel: Int!
  benefitType: String!
  benefitValue: Float!
  description: String
  createdAt: Time!
  updatedAt: Time!
}

type TaskRequirements {
  minAmount: Float
  targetCount: Int
  specificActions: [String!]
}

# Enums
enum TaskType {
  TRADING
  SOCIAL
  REFERRAL
  DEPOSIT
  WITHDRAWAL
  CUSTOM
}

enum TaskFrequency {
  DAILY
  WEEKLY
  MONTHLY
  ONE_TIME
}
