package graphql

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// AdminResolver serves as dependency injection for admin app, add any dependencies you require here.
type AdminResolver struct {
	ActivityCashbackService *resolvers.ActivityCashbackResolver
}

func NewAdminRootResolver() *AdminResolver {
	return &AdminResolver{
		ActivityCashbackService: resolvers.NewActivityCashbackResolver(),
	}
}

// Admin Query resolvers
func (r *AdminResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	return r.ActivityCashbackService.AdminGetAllTasks(ctx)
}

func (r *AdminResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	return r.ActivityCashbackService.AdminGetTaskCompletionStats(ctx, input)
}

func (r *AdminResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	return r.ActivityCashbackService.AdminGetUserActivityStats(ctx, input)
}

func (r *AdminResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	return r.ActivityCashbackService.AdminGetTierDistribution(ctx)
}

func (r *AdminResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	return r.ActivityCashbackService.AdminGetTopUsers(ctx, limit)
}

// Admin Mutation resolvers
func (r *AdminResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	return r.ActivityCashbackService.CreateTask(ctx, input)
}

func (r *AdminResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	return r.ActivityCashbackService.UpdateTask(ctx, input)
}

func (r *AdminResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	return r.ActivityCashbackService.DeleteTask(ctx, taskID)
}

func (r *AdminResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	return r.ActivityCashbackService.CreateTaskCategory(ctx, input)
}

func (r *AdminResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	return r.ActivityCashbackService.UpdateTaskCategory(ctx, input)
}

func (r *AdminResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	return r.ActivityCashbackService.DeleteTaskCategory(ctx, categoryID)
}

func (r *AdminResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	return r.ActivityCashbackService.CreateTierBenefit(ctx, input)
}

func (r *AdminResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	return r.ActivityCashbackService.UpdateTierBenefit(ctx, input)
}

func (r *AdminResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	return r.ActivityCashbackService.DeleteTierBenefit(ctx, tierBenefitID)
}

func (r *AdminResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	return r.ActivityCashbackService.AdminResetDailyTasks(ctx)
}

func (r *AdminResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	return r.ActivityCashbackService.AdminResetWeeklyTasks(ctx)
}

func (r *AdminResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	return r.ActivityCashbackService.AdminResetMonthlyTasks(ctx)
}

func (r *AdminResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	return r.ActivityCashbackService.AdminRecalculateAllUserTiers(ctx)
}

func (r *AdminResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	return r.ActivityCashbackService.AdminSeedInitialTasks(ctx)
}
