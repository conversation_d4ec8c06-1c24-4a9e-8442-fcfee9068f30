package graphql

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// AdminResolver serves as dependency injection for admin app, add any dependencies you require here.
type AdminResolver struct {
	ActivityCashbackService *resolvers.ActivityCashbackResolver
}

func NewAdminRootResolver() *AdminResolver {
	return &AdminResolver{
		ActivityCashbackService: resolvers.NewActivityCashbackResolver(),
	}
}
