scalar Time

# Admin GraphQL Schema - API Key Authentication Required
# This schema contains admin-only operations that require API key authentication

type Query {
  # Admin: Get all tasks
  adminGetAllTasks: [ActivityTask!]!

  # Admin: Get task completion statistics
  adminGetTaskCompletionStats(input: AdminStatsInput!): AdminTaskCompletionStatsResponse!

  # Admin: Get user activity statistics
  adminGetUserActivityStats(input: AdminStatsInput!): AdminUserActivityStatsResponse!

  # Admin: Get tier distribution
  adminGetTierDistribution: AdminTierDistributionResponse!

  # Admin: Get top users by points
  adminGetTopUsers(limit: Int = 10): [UserTierInfo!]!
}

type Mutation {
  # Admin: Create task
  createTask(input: CreateTaskInput!): ActivityTask!

  # Admin: Update task
  updateTask(input: UpdateTaskInput!): ActivityTask!

  # Admin: Delete task
  deleteTask(taskId: ID!): Boolean!

  # Admin: Create task category
  createTaskCategory(input: CreateTaskCategoryInput!): TaskCategory!

  # Admin: Update task category
  updateTaskCategory(input: UpdateTaskCategoryInput!): TaskCategory!

  # Admin: Delete task category
  deleteTaskCategory(categoryId: ID!): Boolean!

  # Admin: Create tier benefit
  createTierBenefit(input: CreateTierBenefitInput!): TierBenefitResponse!

  # Admin: Update tier benefit
  updateTierBenefit(input: UpdateTierBenefitInput!): TierBenefitResponse!

  # Admin: Delete tier benefit
  deleteTierBenefit(tierBenefitId: ID!): Boolean!

  # Admin: Reset daily tasks
  adminResetDailyTasks: Boolean!

  # Admin: Reset weekly tasks
  adminResetWeeklyTasks: Boolean!

  # Admin: Reset monthly tasks
  adminResetMonthlyTasks: Boolean!

  # Admin: Recalculate all user tiers
  adminRecalculateAllUserTiers: Boolean!

  # Admin: Seed initial tasks
  adminSeedInitialTasks: Boolean!
}
