package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// CreateUserWithReferral is the resolver for the createUserWithReferral field.
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	panic(fmt.Errorf("not implemented: CreateUserWithReferral - createUserWithReferral"))
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (r *mutationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	panic(fmt.Errorf("not implemented: CreateUserInvitationCode - createUserInvitationCode"))
}

// UpdateLevelCommission is the resolver for the updateLevelCommission field.
func (r *mutationResolver) UpdateLevelCommission(ctx context.Context, input gql_model.UpdateLevelCommissionInput) (*gql_model.UpdateLevelCommissionResponse, error) {
	panic(fmt.Errorf("not implemented: UpdateLevelCommission - updateLevelCommission"))
}

// ClaimAgentReferral is the resolver for the claimAgentReferral field.
func (r *mutationResolver) ClaimAgentReferral(ctx context.Context, input gql_model.ClaimAgentReferralInput) (*gql_model.ClaimResultResponse, error) {
	panic(fmt.Errorf("not implemented: ClaimAgentReferral - claimAgentReferral"))
}

// CreateInfiniteAgentConfig is the resolver for the createInfiniteAgentConfig field.
func (r *mutationResolver) CreateInfiniteAgentConfig(ctx context.Context, input gql_model.CreateInfiniteAgentConfigInput) (*gql_model.CreateInfiniteAgentConfigResponse, error) {
	panic(fmt.Errorf("not implemented: CreateInfiniteAgentConfig - createInfiniteAgentConfig"))
}

// UpdateInfiniteAgentConfig is the resolver for the updateInfiniteAgentConfig field.
func (r *mutationResolver) UpdateInfiniteAgentConfig(ctx context.Context, input gql_model.UpdateInfiniteAgentConfigInput) (*gql_model.UpdateInfiniteAgentConfigResponse, error) {
	panic(fmt.Errorf("not implemented: UpdateInfiniteAgentConfig - updateInfiniteAgentConfig"))
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context) (*gql_model.ReferralSnapshot, error) {
	panic(fmt.Errorf("not implemented: ReferralSnapshot - referralSnapshot"))
}

// AgentLevels is the resolver for the agentLevels field.
func (r *queryResolver) AgentLevels(ctx context.Context) ([]*gql_model.AgentLevel, error) {
	panic(fmt.Errorf("not implemented: AgentLevels - agentLevels"))
}

// AgentLevel is the resolver for the agentLevel field.
func (r *queryResolver) AgentLevel(ctx context.Context, id int) (*gql_model.AgentLevel, error) {
	panic(fmt.Errorf("not implemented: AgentLevel - agentLevel"))
}

// UserLevelInfo is the resolver for the userLevelInfo field.
func (r *queryResolver) UserLevelInfo(ctx context.Context) (*gql_model.UserLevelInfoResponse, error) {
	panic(fmt.Errorf("not implemented: UserLevelInfo - userLevelInfo"))
}

// TransactionData is the resolver for the transactionData field.
func (r *queryResolver) TransactionData(ctx context.Context, input gql_model.TransactionDataInput) (*gql_model.TransactionDataResponse, error) {
	panic(fmt.Errorf("not implemented: TransactionData - transactionData"))
}

// DataOverview is the resolver for the dataOverview field.
func (r *queryResolver) DataOverview(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.DataOverviewWithSummary, error) {
	panic(fmt.Errorf("not implemented: DataOverview - dataOverview"))
}

// UserInvitationData is the resolver for the userInvitationData field.
func (r *queryResolver) UserInvitationData(ctx context.Context) (*gql_model.UserInvitationDataResponse, error) {
	panic(fmt.Errorf("not implemented: UserInvitationData - userInvitationData"))
}

// RewardData is the resolver for the rewardData field.
func (r *queryResolver) RewardData(ctx context.Context) (*gql_model.RewardDataResponse, error) {
	panic(fmt.Errorf("not implemented: RewardData - rewardData"))
}

// InvitationRecords is the resolver for the invitationRecords field.
func (r *queryResolver) InvitationRecords(ctx context.Context) (*gql_model.InvitationRecordResponse, error) {
	panic(fmt.Errorf("not implemented: InvitationRecords - invitationRecords"))
}

// GetClaimReward is the resolver for the getClaimReward field.
func (r *queryResolver) GetClaimReward(ctx context.Context) (*gql_model.ClaimRewardResponse, error) {
	panic(fmt.Errorf("not implemented: GetClaimReward - getClaimReward"))
}

// InfiniteAgentConfigs is the resolver for the infiniteAgentConfigs field.
func (r *queryResolver) InfiniteAgentConfigs(ctx context.Context) (*gql_model.InfiniteAgentConfigsResponse, error) {
	panic(fmt.Errorf("not implemented: InfiniteAgentConfigs - infiniteAgentConfigs"))
}

// InfiniteAgentConfig is the resolver for the infiniteAgentConfig field.
func (r *queryResolver) InfiniteAgentConfig(ctx context.Context, id string) (*gql_model.InfiniteAgentConfigResponse, error) {
	panic(fmt.Errorf("not implemented: InfiniteAgentConfig - infiniteAgentConfig"))
}

// ReferralTreeSnapshots is the resolver for the referralTreeSnapshots field.
func (r *queryResolver) ReferralTreeSnapshots(ctx context.Context) (*gql_model.ReferralTreeSnapshotsResponse, error) {
	panic(fmt.Errorf("not implemented: ReferralTreeSnapshots - referralTreeSnapshots"))
}

// ReferralTreeSnapshot is the resolver for the referralTreeSnapshot field.
func (r *queryResolver) ReferralTreeSnapshot(ctx context.Context, id string) (*gql_model.ReferralTreeSnapshotResponse, error) {
	panic(fmt.Errorf("not implemented: ReferralTreeSnapshot - referralTreeSnapshot"))
}

// InfiniteAgentReferralTrees is the resolver for the infiniteAgentReferralTrees field.
func (r *queryResolver) InfiniteAgentReferralTrees(ctx context.Context) (*gql_model.InfiniteAgentReferralTreesResponse, error) {
	panic(fmt.Errorf("not implemented: InfiniteAgentReferralTrees - infiniteAgentReferralTrees"))
}

// InfiniteAgentReferralTree is the resolver for the infiniteAgentReferralTree field.
func (r *queryResolver) InfiniteAgentReferralTree(ctx context.Context, id string) (*gql_model.InfiniteAgentReferralTreeResponse, error) {
	panic(fmt.Errorf("not implemented: InfiniteAgentReferralTree - infiniteAgentReferralTree"))
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
