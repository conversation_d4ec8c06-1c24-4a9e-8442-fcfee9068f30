# Admin GraphQL Generation Configuration
# This config generates admin-only GraphQL server code

# Where are all the admin schema files located?
schema:
  - internal/controller/graphql/admin_schema.graphqls
  - internal/controller/graphql/admin_schemas/*.gql

# Where should the generated admin server code go?
exec:
  filename: internal/controller/graphql/admin_generated.go
  package: graphql

# Where should any generated admin models go?
model:
  filename: internal/controller/graphql/gql_model/admin_models_gen.go
  package: gql_model

# Where should the admin resolver implementations go?
resolver:
  layout: follow-schema
  dir: internal/controller/graphql/
  package: graphql
  filename_template: "admin_{name}.resolvers.go"

# Optional: turn on use `gqlgen:"fieldName"` tags in your models
# struct_tag: json

# Optional: turn on to use []Thing instead of []*Thing
# omit_slice_element_pointers: false

# gqlgen will search for any type names in the schema in these go packages
# if they match it will use them, otherwise it will generate them.
autobind:
#  - "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"

# This section declares type mapping between the GraphQL and go type systems
models:
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Int:
    model:
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Time:
    model:
      - github.com/99designs/gqlgen/graphql.Time
