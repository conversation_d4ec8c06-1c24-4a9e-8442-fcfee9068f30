Server = .
ServerName = xbit-agent

# Define variables
GOOS = linux   # Target OS (can be overridden)
GOARCH = amd64 # Target architecture
BinDir = ./bin

# Detect local platform for development
LOCAL_GOOS = $(shell go env GOOS)
LOCAL_GOARCH = $(shell go env GOARCH)

build: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go || { \
		echo "Build failed, trying to regenerate GraphQL code..."; \
		$(MAKE) gqlgen-fix; \
		cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go; \
	}
	@echo "Server built for $(GOOS)/$(GOARCH) in $(BinDir)"

build-local: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go || { \
		echo "Build failed, trying to regenerate GraphQL code..."; \
		$(MAKE) gqlgen-fix; \
		cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go; \
	}
	@echo "Server built for $(LOCAL_GOOS)/$(LOCAL_GOARCH) in $(BinDir)"

gqlgen-check:
	@if [ ! -f "internal/controller/graphql/generated.go" ] || [ ! -f "internal/controller/graphql/admin_generated.go" ]; then \
		echo "Generated GraphQL files not found, generating..."; \
		$(MAKE) gqlgen-all; \
	fi

db-diff:
	# atlas migrate diff [flags] [name]
	atlas migrate diff --env gorm

db-rehash:
	atlas migrate hash --dir file://migrations

db-fix-checksum:
	./scripts/fix-migration-checksum.sh

db-apply:
	./scripts/run.sh local migrate

db-apply-docker:
	./scripts/run.sh docker migrate

# Atlas migrations
db-apply-atlas:
	@if [ -z "$$DATABASE_URL" ]; then \
		echo "Error: DATABASE_URL environment variable is not set"; \
		echo "Please set DATABASE_URL or use 'make db-apply' instead"; \
		exit 1; \
	fi
	atlas migrate apply --url "$$DATABASE_URL" --dir file://migrations

db-apply-atlas-docker:
	atlas migrate apply --url "postgres://postgres:postgres@localhost:5432/agent?sslmode=disable" --dir file://migrations

# Generate both user and admin GraphQL code
gqlgen-all:
	@echo "Generating both User and Admin GraphQL code..."
	@$(MAKE) gqlgen-user
	@$(MAKE) gqlgen-admin
	@echo "All GraphQL code generation completed successfully"

# Generate user GraphQL code
gqlgen-user:
	@echo "Generating User GraphQL code..."
	@echo "Step 1: Backing up existing user resolver files..."
	@if [ -f "internal/controller/graphql/schema.resolvers.go" ]; then \
		mv internal/controller/graphql/schema.resolvers.go internal/controller/graphql/schema.resolvers.go.bak; \
	fi
	@if [ -f "internal/controller/graphql/activity_cashback.resolvers.go" ]; then \
		mv internal/controller/graphql/activity_cashback.resolvers.go internal/controller/graphql/activity_cashback.resolvers.go.bak; \
	fi
	@echo "Step 2: Ensuring dependencies are up to date..."
	@go get github.com/99designs/gqlgen@latest
	@go get golang.org/x/tools/go/packages@latest
	@go get github.com/urfave/cli/v2@latest
	@go mod tidy
	@echo "Step 3: Generating User GraphQL code..."
	@go run github.com/99designs/gqlgen generate --config gqlgen.yml || { \
		echo "User GraphQL generation failed, trying alternative approach..."; \
		echo "Removing existing generated files..."; \
		rm -f internal/controller/graphql/generated.go; \
		rm -f internal/controller/graphql/gql_model/models_gen.go; \
		echo "Cleaning module cache (this may take a moment)..."; \
		go clean -modcache || echo "Module cache clean failed, continuing..."; \
		echo "Reinstalling dependencies..."; \
		go get github.com/99designs/gqlgen@latest; \
		go get golang.org/x/tools/go/packages@latest; \
		go get github.com/urfave/cli/v2@latest; \
		go mod download; \
		go mod tidy; \
		echo "Retrying User GraphQL generation..."; \
		go run github.com/99designs/gqlgen generate --config gqlgen.yml || { \
			echo "Second attempt failed, trying with clean slate..."; \
			rm -rf internal/controller/graphql/gql_model; \
			mkdir -p internal/controller/graphql/gql_model; \
			go run github.com/99designs/gqlgen generate --config gqlgen.yml; \
		}; \
	}
	@echo "Step 4: Restoring user resolver files..."
	@if [ -f "internal/controller/graphql/schema.resolvers.go.bak" ]; then \
		mv internal/controller/graphql/schema.resolvers.go.bak internal/controller/graphql/schema.resolvers.go; \
	fi
	@if [ -f "internal/controller/graphql/activity_cashback.resolvers.go.bak" ]; then \
		mv internal/controller/graphql/activity_cashback.resolvers.go.bak internal/controller/graphql/activity_cashback.resolvers.go; \
	fi
	@echo "User GraphQL code generation completed successfully"

# Generate admin GraphQL code
gqlgen-admin:
	@echo "Generating Admin GraphQL code..."
	@echo "Step 1: Backing up existing admin resolver files..."
	@if [ -f "internal/controller/graphql/admin_schema.resolvers.go" ]; then \
		mv internal/controller/graphql/admin_schema.resolvers.go internal/controller/graphql/admin_schema.resolvers.go.bak; \
	fi
	@echo "Step 2: Generating Admin GraphQL code..."
	@go run github.com/99designs/gqlgen generate --config admin_gqlgen.yml || { \
		echo "Admin GraphQL generation failed, trying alternative approach..."; \
		echo "Removing existing admin generated files..."; \
		rm -f internal/controller/graphql/admin_generated.go; \
		rm -f internal/controller/graphql/gql_model/admin_models_gen.go; \
		echo "Retrying Admin GraphQL generation..."; \
		go run github.com/99designs/gqlgen generate --config admin_gqlgen.yml || { \
			echo "Second attempt failed, trying with clean slate..."; \
			mkdir -p internal/controller/graphql/gql_model; \
			go run github.com/99designs/gqlgen generate --config admin_gqlgen.yml; \
		}; \
	}
	@echo "Step 3: Restoring admin resolver files..."
	@if [ -f "internal/controller/graphql/admin_schema.resolvers.go.bak" ]; then \
		mv internal/controller/graphql/admin_schema.resolvers.go.bak internal/controller/graphql/admin_schema.resolvers.go; \
	fi
	@echo "Admin GraphQL code generation completed successfully"

# Legacy gqlgen target (now calls gqlgen-all for backward compatibility)
gqlgen:
	@echo "Running legacy gqlgen target (now generates both user and admin)..."
	@$(MAKE) gqlgen-all

gqlgen-clean:
	@echo "Cleaning and regenerating all GraphQL code..."
	@$(MAKE) gqlgen-clean-user
	@$(MAKE) gqlgen-clean-admin
	@echo "All GraphQL code cleaned and regenerated successfully"

gqlgen-clean-user:
	@echo "Cleaning and regenerating User GraphQL code..."
	@echo "Step 1: Backing up user resolver files..."
	@if [ -f "internal/controller/graphql/schema.resolvers.go" ]; then \
		mv internal/controller/graphql/schema.resolvers.go internal/controller/graphql/schema.resolvers.go.bak; \
	fi
	@if [ -f "internal/controller/graphql/activity_cashback.resolvers.go" ]; then \
		mv internal/controller/graphql/activity_cashback.resolvers.go internal/controller/graphql/activity_cashback.resolvers.go.bak; \
	fi
	@echo "Step 2: Removing user generated files..."
	@rm -f internal/controller/graphql/generated.go
	@rm -f internal/controller/graphql/gql_model/models_gen.go
	@echo "Step 3: Generating fresh User GraphQL code..."
	@go run github.com/99designs/gqlgen generate --config gqlgen.yml
	@echo "Step 4: Restoring user resolver files..."
	@if [ -f "internal/controller/graphql/schema.resolvers.go.bak" ]; then \
		mv internal/controller/graphql/schema.resolvers.go.bak internal/controller/graphql/schema.resolvers.go; \
	fi
	@if [ -f "internal/controller/graphql/activity_cashback.resolvers.go.bak" ]; then \
		mv internal/controller/graphql/activity_cashback.resolvers.go.bak internal/controller/graphql/activity_cashback.resolvers.go; \
	fi
	@echo "User GraphQL code cleaned and regenerated successfully"

gqlgen-clean-admin:
	@echo "Cleaning and regenerating Admin GraphQL code..."
	@echo "Step 1: Backing up admin resolver files..."
	@if [ -f "internal/controller/graphql/admin_schema.resolvers.go" ]; then \
		mv internal/controller/graphql/admin_schema.resolvers.go internal/controller/graphql/admin_schema.resolvers.go.bak; \
	fi
	@echo "Step 2: Removing admin generated files..."
	@rm -f internal/controller/graphql/admin_generated.go
	@rm -f internal/controller/graphql/gql_model/admin_models_gen.go
	@echo "Step 3: Generating fresh Admin GraphQL code..."
	@go run github.com/99designs/gqlgen generate --config admin_gqlgen.yml
	@echo "Step 4: Restoring admin resolver files..."
	@if [ -f "internal/controller/graphql/admin_schema.resolvers.go.bak" ]; then \
		mv internal/controller/graphql/admin_schema.resolvers.go.bak internal/controller/graphql/admin_schema.resolvers.go; \
	fi
	@echo "Admin GraphQL code cleaned and regenerated successfully"

# Check GraphQL generation status
gqlgen-status:
	@echo "=== GraphQL Generation Status ==="
	@echo ""
	@echo "User GraphQL Files:"
	@echo "  Generated file exists: $(shell test -f internal/controller/graphql/generated.go && echo 'YES' || echo 'NO')"
	@echo "  Models file exists: $(shell test -f internal/controller/graphql/gql_model/models_gen.go && echo 'YES' || echo 'NO')"
	@echo ""
	@echo "Admin GraphQL Files:"
	@echo "  Admin generated file exists: $(shell test -f internal/controller/graphql/admin_generated.go && echo 'YES' || echo 'NO')"
	@echo "  Admin models file exists: $(shell test -f internal/controller/graphql/gql_model/admin_models_gen.go && echo 'YES' || echo 'NO')"
	@echo ""
	@echo "Schema files found:"
	@find . -name "*.graphqls" -o -name "*.gql" | head -15 || echo "No schema files found"
	@echo ""
	@echo "Resolver files:"
	@ls -la internal/controller/graphql/*.resolvers.go 2>/dev/null || echo "No resolver files found"
	@echo ""
	@echo "Backup files:"
	@ls -la internal/controller/graphql/*.resolvers.go.bak 2>/dev/null || echo "No backup files found"
	@echo ""
	@echo "Configuration files:"
	@echo "  User config (gqlgen.yml): $(shell test -f gqlgen.yml && echo 'EXISTS' || echo 'MISSING')"
	@echo "  Admin config (admin_gqlgen.yml): $(shell test -f admin_gqlgen.yml && echo 'EXISTS' || echo 'MISSING')"
	@echo "=== End Status ==="

gqlgen-fix:
	@echo "Fixing GraphQL generation issues..."
	@echo "This will use the same robust approach as 'make gqlgen'"
	@$(MAKE) gqlgen

# Prepare environment for GraphQL generation
gqlgen-prepare:
	@echo "Preparing environment for GraphQL generation..."
	@echo "Cleaning Go module cache..."
	@go clean -modcache
	@echo "Installing required dependencies..."
	@go get github.com/99designs/gqlgen@latest
	@go get golang.org/x/tools/go/packages@latest
	@go get github.com/urfave/cli/v2@latest
	@go mod download
	@go mod tidy
	@echo "Environment prepared successfully"

# Run GraphQL generation doctor
gqlgen-doctor:
	@echo "Running GraphQL generation diagnostics..."
	@./scripts/gqlgen-doctor.sh

# Run GraphQL generation doctor with fixes
gqlgen-doctor-fix:
	@echo "Running GraphQL generation diagnostics with auto-fix..."
	@./scripts/gqlgen-doctor.sh --fix

install-deps:
	@echo "Installing dependencies..."
	go mod tidy
	go get github.com/99designs/gqlgen@latest
	go get golang.org/x/tools/go/packages@latest
	go mod tidy
	@echo "Dependencies installed"

# Activity Cashback specific targets
activity-cashback-test:
	@echo "Running Activity Cashback tests..."
	@if [ -f "scripts/test_activity_cashback.sh" ]; then \
		chmod +x scripts/test_activity_cashback.sh; \
		./scripts/test_activity_cashback.sh; \
	else \
		echo "Test script not found, running Go tests..."; \
		go test -v ./internal/service/activity_cashback/...; \
	fi

activity-cashback-build:
	@echo "Building with Activity Cashback system..."
	@$(MAKE) gqlgen-check
	@$(MAKE) build-local

activity-cashback-dev:
	@echo "Starting development with Activity Cashback system..."
	@$(MAKE) gqlgen-check
	@$(MAKE) dev

# Debug and troubleshooting targets
debug-build:
	@echo "Debug build with verbose output..."
	mkdir -p $(BinDir)
	cd $(Server) && go build -v -x -o $(BinDir)/$(ServerName) cmd/graphql/main.go

debug-gqlgen:
	@echo "Debug GraphQL generation..."
	@echo "Current working directory: $(shell pwd)"
	@echo "GraphQL config file exists: $(shell test -f gqlgen.yml && echo 'YES' || echo 'NO')"
	@echo "Generated file exists: $(shell test -f internal/controller/graphql/generated.go && echo 'YES' || echo 'NO')"
	@echo "Schema files:"
	@find . -name "*.graphqls" -o -name "*.gql" | head -10
	@echo "Running GraphQL generation with verbose output..."
	go run github.com/99designs/gqlgen generate --config gqlgen.yml --verbose

fix-imports:
	@echo "Fixing Go imports..."
	@go install golang.org/x/tools/cmd/goimports@latest
	@export PATH="$(shell go env GOPATH)/bin:$$PATH"; \
	if command -v goimports >/dev/null 2>&1; then \
		find . -name "*.go" -not -path "./vendor/*" -not -path "./.git/*" | xargs goimports -w; \
		echo "Imports fixed with goimports"; \
	else \
		echo "Using go run instead of goimports..."; \
		find . -name "*.go" -not -path "./vendor/*" -not -path "./.git/*" -exec go run golang.org/x/tools/cmd/goimports -w {} \;; \
		echo "Imports fixed with go run goimports"; \
	fi

format-code:
	@echo "Formatting Go code..."
	go fmt ./...
	@$(MAKE) fix-imports
	@echo "Code formatted"

run:
	go run cmd/graphql/main.go

run-local:
	./scripts/run.sh local run

run-docker:
	docker-compose up -d

dev:
	./scripts/run.sh local dev

dev-air:
	air -c .air.toml

test:
	go test ./...

test-verbose:
	go test -v ./...

test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

test-unit:
	go test -v ./internal/utils/... ./internal/service/... ./internal/controller/...

test-integration:
	go test -v -tags=integration ./...

test-watch:
	@echo "Running tests in watch mode (requires entr)..."
	find . -name "*.go" | entr -c go test ./...

clean:
	rm -rf $(BinDir)

# JWT Token Generation for Testing
jwt-token:
	@echo "Generating JWT token for local testing..."
	./scripts/generate-jwt.sh

jwt-token-help:
	./scripts/generate-jwt.sh --help

jwt-token-custom:
	@echo "Usage: make jwt-token-custom USER_ID=<uuid> EMAIL=<email>"
	@if [ -z "$(USER_ID)" ] && [ -z "$(EMAIL)" ]; then \
		echo "Example: make jwt-token-custom EMAIL=<EMAIL>"; \
		echo "Example: make jwt-token-custom USER_ID=123e4567-e89b-12d3-a456-************ EMAIL=<EMAIL>"; \
		./scripts/generate-jwt.sh; \
	else \
		./scripts/generate-jwt.sh $(if $(USER_ID),-u $(USER_ID)) $(if $(EMAIL),-e $(EMAIL)); \
	fi

jwt-token-test:
	@echo "Testing JWT token against User GraphQL endpoint..."
	./scripts/test-jwt-token.sh

# Test both GraphQL endpoints
test-graphql-endpoints:
	@echo "Testing both User and Admin GraphQL endpoints..."
	@echo "Testing User endpoint with JWT..."
	@curl -s -X POST http://localhost:8080/api/dex-agent/graphql \
		-H "Content-Type: application/json" \
		-H "Authorization: Bearer test-jwt-token" \
		-d '{"query": "{ __schema { queryType { name } } }"}' || echo "User endpoint test failed"
	@echo ""
	@echo "Testing Admin endpoint with API key..."
	@curl -s -X POST http://localhost:8080/api/dex-agent/admin/graphql \
		-H "Content-Type: application/json" \
		-H "x-api-key: local-internal-api-key-for-development-only" \
		-d '{"query": "{ __schema { queryType { name } } }"}' || echo "Admin endpoint test failed"
	@echo ""
	@echo "GraphQL endpoints test completed"

# Activity Cashback Task Management
reseed-tasks:
	@echo "Reseeding activity cashback tasks..."
	go run cmd/reseed-tasks/main.go -config config.yaml

reseed-tasks-dev:
	@echo "Reseeding activity cashback tasks (dev environment)..."
	go run cmd/reseed-tasks/main.go -config config-dev.yaml

# Help target
help:
	@echo "=== xbit-agent Makefile Help ==="
	@echo ""
	@echo "Build targets:"
	@echo "  build              - Build the server for Linux"
	@echo "  build-local        - Build the server for local platform"
	@echo "  run                - Run the server locally"
	@echo "  dev                - Run the server in development mode"
	@echo "  clean              - Clean build artifacts"
	@echo ""
	@echo "GraphQL targets:"
	@echo "  gqlgen             - Generate both User and Admin GraphQL code (legacy, calls gqlgen-all)"
	@echo "  gqlgen-all         - Generate both User and Admin GraphQL code"
	@echo "  gqlgen-user        - Generate User GraphQL code only"
	@echo "  gqlgen-admin       - Generate Admin GraphQL code only"
	@echo "  gqlgen-clean       - Clean and regenerate both User and Admin GraphQL code"
	@echo "  gqlgen-clean-user  - Clean and regenerate User GraphQL code only"
	@echo "  gqlgen-clean-admin - Clean and regenerate Admin GraphQL code only"
	@echo "  gqlgen-fix         - Fix GraphQL generation issues (alias for gqlgen-all)"
	@echo "  gqlgen-status      - Check GraphQL generation status for both User and Admin"
	@echo "  gqlgen-prepare     - Prepare environment for GraphQL generation"
	@echo "  gqlgen-check       - Check if GraphQL generation is needed"
	@echo "  gqlgen-doctor      - Run GraphQL generation diagnostics"
	@echo "  gqlgen-doctor-fix  - Run GraphQL diagnostics with auto-fix"
	@echo ""
	@echo "Testing targets:"
	@echo "  test               - Run all tests"
	@echo "  test-verbose       - Run tests with verbose output"
	@echo "  test-coverage      - Run tests with coverage report"
	@echo "  test-unit          - Run unit tests only"
	@echo "  test-integration   - Run integration tests only"
	@echo "  test-watch         - Run tests in watch mode"
	@echo ""
	@echo "Database targets:"
	@echo "  db-diff            - Generate database migration diff"
	@echo "  db-rehash          - Rehash database migrations"
	@echo "  db-apply           - Apply database migrations"
	@echo "  db-apply-docker    - Apply database migrations in Docker"
	@echo ""
	@echo "Development targets:"
	@echo "  install-deps       - Install dependencies"
	@echo "  fix-imports        - Fix Go imports using goimports"
	@echo "  format-code        - Format Go code and fix imports"
	@echo "  debug-build        - Debug build with verbose output"
	@echo "  debug-gqlgen       - Debug GraphQL generation"
	@echo ""
	@echo "JWT & Testing targets:"
	@echo "  jwt-token          - Generate JWT token for local testing"
	@echo "  jwt-token-help     - Show JWT token generation help"
	@echo "  jwt-token-custom   - Generate custom JWT token"
	@echo "  jwt-token-test     - Test JWT token against User GraphQL endpoint"
	@echo "  test-graphql-endpoints - Test both User and Admin GraphQL endpoints"
	@echo ""
	@echo "Activity Cashback targets:"
	@echo "  activity-cashback-test  - Test activity cashback system"
	@echo "  activity-cashback-build - Build with activity cashback system"
	@echo "  activity-cashback-dev   - Run activity cashback in dev mode"
	@echo "  reseed-tasks       - Reseed activity cashback tasks"
	@echo "  reseed-tasks-dev   - Reseed activity cashback tasks (dev)"
	@echo ""
	@echo "Usage: make <target>"
	@echo "Example: make gqlgen"

.PHONY: build build-local gqlgen-check gqlgen gqlgen-all gqlgen-user gqlgen-admin gqlgen-clean gqlgen-clean-user gqlgen-clean-admin gqlgen-fix gqlgen-status gqlgen-prepare gqlgen-doctor gqlgen-doctor-fix db-diff db-rehash db-fix-checksum db-apply db-apply-docker db-apply-atlas db-apply-atlas-docker install-deps activity-cashback-test activity-cashback-build activity-cashback-dev debug-build debug-gqlgen fix-imports format-code run dev test test-verbose test-coverage test-unit test-integration test-watch clean jwt-token jwt-token-help jwt-token-custom jwt-token-test reseed-tasks reseed-tasks-dev help
